<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码提取测试页面 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="logo.png">
    <!-- 引入 MQTT 客户端库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <!-- 引入通用参数配置样式 -->
    <link rel="stylesheet" href="common/parameter-config.css">
    <!-- 引入通用参数配置脚本 -->
    <script src="common/parameter-config.js"></script>
</head>
<body>
    <!-- MQTT 连接状态指示器 -->
    <div class="mqtt-status-container">
        <div class="mqtt-connection-status disconnected" id="mqtt-status">MQTT 连接中...</div>
        <div class="data-timestamp" id="data-timestamp">等待数据...</div>
    </div>

    <div class="container">
        <div class="header">
            <h1>代码提取测试页面</h1>
        </div>

        <div class="main-content">
            <!-- 运行状态面板 -->
            <div class="protection-panel">
                <div class="panel-title">运行状态测试</div>
                <table class="params-table">
                    <thead>
                        <tr>
                            <th>操作</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="button-cell">
                                <button class="control-button start-button" onclick="startOperation()">启动</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="start-indicator"></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="button-cell">
                                <button class="control-button stop-button" onclick="stopOperation()">停止</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator stop" id="stop-indicator"></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="button-cell">
                                <button class="control-button reset-button" onclick="resetOperation()">复位</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="reset-indicator"></div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 主控方式面板 -->
            <div class="protection-panel">
                <div class="panel-title">主控方式测试</div>
                <table class="params-table">
                    <thead>
                        <tr>
                            <th>控制模式</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="button-cell">
                                <button class="option-button" data-value="fixedCompensation" onclick="selectMainControl(this, ['fixedCompensation', 'dynamicCompensation', 'powerFactor'])">固定补偿</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="fixed-indicator"></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="button-cell">
                                <button class="option-button" data-value="dynamicCompensation" onclick="selectMainControl(this, ['fixedCompensation', 'dynamicCompensation', 'powerFactor'])">动态补偿</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="dynamic-indicator"></div>
                            </td>
                        </tr>
                        <tr>
                            <td class="button-cell">
                                <button class="option-button" data-value="powerFactor" onclick="selectMainControl(this, ['fixedCompensation', 'dynamicCompensation', 'powerFactor'])">功率因数</button>
                            </td>
                            <td class="status-cell">
                                <div class="status-indicator" id="power-indicator"></div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 状态提示消息 -->
        <div id="status-message" class="status-message"></div>
    </div>

    <script>
        // 测试页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('代码提取测试页面初始化...');

            // 测试设备操作管理器
            console.log('=== 测试设备操作管理器 ===');
            const deviceManager = initDeviceOperationManager();
            console.log('设备操作管理器初始化完成:', deviceManager);

            // 测试旁路控制管理器
            console.log('=== 测试旁路控制管理器 ===');
            const bypassManager = initBypassControlManager();
            console.log('旁路控制管理器初始化完成:', bypassManager);

            // 测试通用函数
            console.log('=== 测试通用函数 ===');
            console.log('showStatusMessage 函数:', typeof showStatusMessage);
            console.log('updateMQTTStatus 函数:', typeof updateMQTTStatus);
            console.log('updateDataTimestamp 函数:', typeof updateDataTimestamp);

            // 显示测试完成消息
            showStatusMessage('代码提取测试完成！所有管理器和函数都已正确加载。', 'success');

            console.log('=== 测试完成 ===');
            console.log('可用的全局对象:');
            console.log('- deviceOperationManager:', window.deviceOperationManager);
            console.log('- bypassControlManager:', window.bypassControlManager);
            console.log('- parameterManager:', window.parameterManager);
        });
    </script>
</body>
</html>
