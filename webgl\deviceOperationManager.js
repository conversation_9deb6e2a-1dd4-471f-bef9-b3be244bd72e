/**
 * 设备操作管理器类
 * 用于管理MQTT设备操作，包括启动、停止、复位等操作
 */
class DeviceOperationManager {
    constructor() {
        this.mqttClient = null;
        this.messageCallback = null;
        this.isConnected = false;
        this.operationTopics = {
            start: '/device/svg/start',
            stop: '/device/svg/stop',
            reset: '/device/svg/reset'
        };
        this.responseTopic = '/device/svg/response';
    }

    /**
     * 初始化MQTT连接
     */
    async init() {
        try {
            // 创建MQTT客户端连接
            this.mqttClient = this.createMQTTClient();
            
            // 设置消息处理器
            this.mqttClient.on('message', (topic, message) => {
                if (this.messageCallback) {
                    this.messageCallback(topic, message.toString());
                }
            });

            // 订阅响应主题
            await this.subscribe(this.responseTopic);
            
            this.isConnected = true;
            console.log('✅ MQTT设备操作管理器初始化成功');
        } catch (error) {
            console.error('❌ MQTT设备操作管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 创建MQTT客户端
     */
    createMQTTClient() {
        // 使用简化的MQTT客户端实现
        const clientId = 'web-svg-' + Math.random().toString(16).substr(2, 8);
        
        // 模拟MQTT客户端对象
        const mockClient = {
            on: function(event, callback) {
                console.log(`[Mock MQTT] 注册事件监听器: ${event}`);
                if (event === 'message') {
                    // 模拟消息接收
                    setTimeout(() => {
                        if (Math.random() > 0.3) { // 70%概率成功接收消息
                            const mockResponse = {
                                operation: 'start',
                                status: 'success',
                                timestamp: new Date().toISOString()
                            };
                            callback('/device/svg/response', JSON.stringify(mockResponse));
                        }
                    }, 2000);
                }
            },
            publish: function(topic, message, options, callback) {
                console.log(`[Mock MQTT] 发布消息到主题: ${topic}`, message);
                setTimeout(() => {
                    callback(null);
                }, 500);
            },
            subscribe: function(topic, options, callback) {
                console.log(`[Mock MQTT] 订阅主题: ${topic}`);
                setTimeout(() => {
                    callback(null, [{topic: topic, qos: 1}]);
                }, 300);
            },
            end: function() {
                console.log('[Mock MQTT] 连接已关闭');
            }
        };

        return mockClient;
    }

    /**
     * 订阅MQTT主题
     */
    subscribe(topic) {
        return new Promise((resolve, reject) => {
            if (!this.mqttClient) {
                reject(new Error('MQTT客户端未初始化'));
                return;
            }

            this.mqttClient.subscribe(topic, { qos: 1 }, (err, granted) => {
                if (err) {
                    reject(err);
                } else {
                    console.log(`✅ 成功订阅主题: ${topic}`);
                    resolve(granted);
                }
            });
        });
    }

    /**
     * 设置消息回调函数
     */
    setMessageCallback(callback) {
        this.messageCallback = callback;
    }

    /**
     * 启动设备操作
     */
    async startOperation() {
        if (!this.isConnected || !this.mqttClient) {
            throw new Error('MQTT未连接');
        }

        const message = JSON.stringify({
            operation: 'start',
            timestamp: new Date().toISOString()
        });

        return new Promise((resolve, reject) => {
            this.mqttClient.publish(
                this.operationTopics.start,
                message,
                { qos: 1 },
                (err) => {
                    if (err) {
                        reject(new Error('启动指令发送失败'));
                    } else {
                        console.log('✅ 启动指令已发送');
                        resolve();
                    }
                }
            );
        });
    }

    /**
     * 停止设备操作
     */
    async stopOperation() {
        if (!this.isConnected || !this.mqttClient) {
            throw new Error('MQTT未连接');
        }

        const message = JSON.stringify({
            operation: 'stop',
            timestamp: new Date().toISOString()
        });

        return new Promise((resolve, reject) => {
            this.mqttClient.publish(
                this.operationTopics.stop,
                message,
                { qos: 1 },
                (err) => {
                    if (err) {
                        reject(new Error('停止指令发送失败'));
                    } else {
                        console.log('✅ 停止指令已发送');
                        resolve();
                    }
                }
            );
        });
    }

    /**
     * 复位设备操作
     */
    async resetOperation() {
        if (!this.isConnected || !this.mqttClient) {
            throw new Error('MQTT未连接');
        }

        const message = JSON.stringify({
            operation: 'reset',
            timestamp: new Date().toISOString()
        });

        return new Promise((resolve, reject) => {
            this.mqttClient.publish(
                this.operationTopics.reset,
                message,
                { qos: 1 },
                (err) => {
                    if (err) {
                        reject(new Error('复位指令发送失败'));
                    } else {
                        console.log('✅ 复位指令已发送');
                        resolve();
                    }
                }
            );
        });
    }

    /**
     * 关闭MQTT连接
     */
    async disconnect() {
        if (this.mqttClient) {
            this.mqttClient.end();
            this.mqttClient = null;
            this.isConnected = false;
            console.log('✅ MQTT连接已关闭');
        }
    }
}

// 将DeviceOperationManager类添加到全局作用域，以便在HTML文件中使用
if (typeof window !== 'undefined') {
    window.DeviceOperationManager = DeviceOperationManager;
}