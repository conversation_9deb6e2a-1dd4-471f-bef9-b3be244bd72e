# 通用参数配置代码提取说明

## 概述

本次代码提取将三个HTML页面中的CSS样式和JavaScript代码整合到公共文件中，实现代码复用和统一管理。

## 源文件

- `debug1/设备操作.html` - 简化版设备操作页面
- `debug2/设备操作.html` - 完整版设备操作页面  
- `debug2/旁路控制.html` - 旁路控制页面

## 公共文件

### CSS样式文件：`parameter-config.css`

**新增的通用样式类：**

1. **设备操作按钮样式**
   - `.control-button` - 通用控制按钮基础样式
   - `.control-button.start-button:hover` - 启动按钮悬停效果
   - `.control-button.stop-button:hover` - 停止按钮悬停效果
   - `.control-button.reset-button:hover` - 复位按钮悬停效果

2. **选项按钮样式**
   - `.option-button` - 选项按钮基础样式
   - `.option-button:hover` - 悬停效果
   - `.option-button.active` - 激活状态样式

3. **状态指示器样式**
   - `.status-indicator` - 基础指示器样式
   - `.status-indicator.active` - 激活状态（绿色）
   - `.status-indicator.stop` - 停止状态（红色）

4. **表格单元格样式**
   - `.button-cell` - 按钮单元格样式
   - `.status-cell` - 状态单元格样式

5. **旁路控制专用样式**
   - `.control-header` - 控制头部样式
   - `.unit-count` - 单元数量显示样式
   - `.unit-count-select` - 单元数量选择器样式
   - `.phase-panels` - 三相面板容器样式
   - `.phase-panel` - 单个相位面板样式
   - `.phase-title` - 相位标题样式
   - `.phase-table` - 相位表格样式
   - `.bypass-button` - 旁路按钮样式
   - `.unit-id` - 单元ID样式
   - `.value-cell` - 数值单元格样式

6. **MQTT连接状态增强**
   - `.mqtt-connection-status.connecting` - 连接中状态（橙色）

### JavaScript功能文件：`parameter-config.js`

**新增的管理器类：**

1. **DeviceOperationManager 类**
   - 负责设备启动、停止、复位等操作
   - 管理多个MQTT客户端连接
   - 处理主控方式和辅控方式选择
   - 更新状态指示器和按钮样式

2. **BypassControlManager 类**
   - 负责旁路控制功能
   - 管理三相（A、B、C）旁路状态
   - 处理位值计算和MQTT通信
   - 支持动态单元数量调整

**新增的通用函数：**

1. **管理器初始化函数**
   - `initDeviceOperationManager()` - 初始化设备操作管理器
   - `initBypassControlManager()` - 初始化旁路控制管理器

2. **设备操作通用函数**
   - `startOperation()` - 启动操作
   - `stopOperation()` - 停止操作
   - `resetOperation()` - 复位操作
   - `selectMainControl(button, supportedModes)` - 主控方式选择
   - `selectAuxiliaryControl(button, supportedModes)` - 辅控方式选择

3. **旁路控制通用函数**
   - `toggleBypass(phase, unitId, bitIndex)` - 切换旁路状态
   - `updateUnitCount()` - 更新单元数量
   - `updatePhaseTables(unitCount)` - 更新三相表格
   - `downloadBypassSettings()` - 下载旁路设置
   - `initPhaseTables()` - 初始化三相表格

4. **辅助函数**
   - `getIndicatorId(value, type)` - 获取状态指示器ID

## 使用方法

### 1. 引入公共文件

```html
<!-- 引入通用参数配置样式 -->
<link rel="stylesheet" href="../common/parameter-config.css">
<!-- 引入通用参数配置脚本 -->
<script src="../common/parameter-config.js"></script>
```

### 2. 设备操作页面使用示例

```javascript
document.addEventListener('DOMContentLoaded', function() {
    // 初始化设备操作管理器
    const manager = initDeviceOperationManager();
    
    // 初始化MQTT连接
    manager.initMQTTConnections();
});
```

### 3. 旁路控制页面使用示例

```javascript
document.addEventListener('DOMContentLoaded', function() {
    // 初始化旁路控制管理器
    const manager = initBypassControlManager();
    
    // 初始化三相表格
    initPhaseTables();
    
    // 初始化MQTT连接
    manager.initMQTTConnection();
});
```

### 4. 按钮调用示例

```html
<!-- 主控方式按钮 -->
<button class="option-button" data-value="fixedCompensation" 
        onclick="selectMainControl(this, ['fixedCompensation', 'dynamicCompensation', 'powerFactor'])">
    固定补偿
</button>

<!-- 辅控方式按钮 -->
<button class="option-button" data-value="harmonicElimination" 
        onclick="selectAuxiliaryControl(this, ['harmonicElimination'])">
    谐波消除
</button>

<!-- 旁路控制按钮 -->
<button class="bypass-button" id="btn-A01" 
        onclick="toggleBypass('A', 'A01', 0)">
    正常
</button>
```

## 兼容性说明

1. **向后兼容**：所有原有功能保持不变
2. **配置灵活**：支持不同页面使用不同的功能子集
3. **模块化设计**：各管理器独立工作，互不干扰

## 测试页面

可以使用 `test-extraction.html` 页面测试提取的代码是否正常工作。

## 注意事项

1. 确保在使用前先引入MQTT客户端库
2. 各页面需要根据实际需求调用相应的初始化函数
3. 按钮的 `onclick` 事件需要传入正确的支持模式参数
4. 旁路控制页面需要确保HTML结构中包含必要的元素ID

## 维护建议

1. 新增功能时优先考虑添加到公共文件中
2. 定期检查各页面是否可以进一步复用公共代码
3. 保持公共文件的文档更新
